local Fluent = loadstring(game:HttpGet("https://github.com/dawid-scripts/Fluent/releases/latest/download/main.lua"))()
local SaveManager = loadstring(game:HttpGet("https://raw.githubusercontent.com/dawid-scripts/Fluent/master/Addons/SaveManager.lua"))()
local InterfaceManager = loadstring(game:HttpGet("https://raw.githubusercontent.com/dawid-scripts/Fluent/master/Addons/InterfaceManager.lua"))()

-- Add required services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local LocalPlayer = Players.LocalPlayer
local PlayerGui = LocalPlayer.PlayerGui
local GameEvents = ReplicatedStorage.GameEvents

-- Seed stock variables
local SeedStock = {}
local SelectedSeedStock = { Selected = {} } -- Changed to table for multiple seeds
local lastStockCheck = {}
local autoBuyEnabled = false
local stockCheckConnection = nil

-- Function to get seed stock
local function GetSeedStock(IgnoreNoStock: boolean?): table
	local SeedShop = PlayerGui.Seed_Shop
	local Items = SeedShop:FindFirstChild("Blueberry", true).Parent

	local NewList = {}

	for _, Item in next, Items:GetChildren() do
		local MainFrame = Item:FindFirstChild("Main_Frame")
		if not MainFrame then continue end

		local StockText = MainFrame.Stock_Text.Text
		local StockCount = tonumber(StockText:match("%d+"))

		--// Seperate list
		if IgnoreNoStock then
			if StockCount <= 0 then continue end
			NewList[Item.Name] = StockCount
			continue
		end

		SeedStock[Item.Name] = StockCount
	end

	return IgnoreNoStock and NewList or SeedStock
end

-- Function to buy seed
local function BuySeed(Seed: string)
	GameEvents.BuySeedStock:FireServer(Seed)
end

-- Function to buy all selected seeds
local function BuyAllSelectedSeeds()
    local selectedSeeds = SelectedSeedStock.Selected

    print("BuyAllSelectedSeeds called")
    print("Selected seeds type:", type(selectedSeeds))

    -- Handle different types of selectedSeeds
    if type(selectedSeeds) == "table" then
        print("Selected seeds table:")
        for k, v in pairs(selectedSeeds) do
            print("  ", k, "=", v)
        end
    else
        print("Selected seeds value:", selectedSeeds)
    end

    -- Check if we have any selected seeds - handle both table and other formats
    local seedsToProcess = {}

    if type(selectedSeeds) == "table" then
        -- If it's a table, check if it has values
        local hasSeeds = false
        for k, v in pairs(selectedSeeds) do
            if v == true or (type(v) == "string" and v ~= "") then
                table.insert(seedsToProcess, type(k) == "string" and k or v)
                hasSeeds = true
            elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                table.insert(seedsToProcess, v)
                hasSeeds = true
            end
        end

        if not hasSeeds then
            print("No seeds selected (empty table)")
            return
        end
    else
        print("Selected seeds is not a table")
        return
    end

    print("Seeds to process:", seedsToProcess)

    -- Update stock before buying
    GetSeedStock()

    -- Iterate through all selected seeds
    for _, seedName in pairs(seedsToProcess) do
        local stock = SeedStock[seedName]
        print("Checking seed:", seedName, "Stock:", stock)

        -- Only buy if seed is in stock
        if stock and stock > 0 then
            print("Buying", stock, "of", seedName)
            for i = 1, stock do
                BuySeed(seedName)
            end
        else
            print("Seed", seedName, "not in stock or stock is 0")
        end
    end
end

-- Function to check stock changes and auto-buy
local function startStockChecking()
    stockCheckConnection = game:GetService("RunService").Heartbeat:Connect(function()
        if not autoBuyEnabled or not SelectedSeedStock.Selected then return end

        -- Check if we have selected seeds
        local hasSelectedSeeds = false
        if type(SelectedSeedStock.Selected) == "table" then
            for k, v in pairs(SelectedSeedStock.Selected) do
                if v == true or (type(v) == "string" and v ~= "") then
                    hasSelectedSeeds = true
                    break
                end
            end
        end

        if not hasSelectedSeeds then return end

        local currentStock = GetSeedStock()
        local selectedSeeds = SelectedSeedStock.Selected
        local shouldBuy = false

        -- Process selected seeds based on their format
        local seedsToCheck = {}
        if type(selectedSeeds) == "table" then
            for k, v in pairs(selectedSeeds) do
                if v == true or (type(v) == "string" and v ~= "") then
                    local seedName = type(k) == "string" and k or v
                    if type(seedName) == "string" and seedName ~= "" then
                        table.insert(seedsToCheck, seedName)
                    end
                elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                    table.insert(seedsToCheck, v)
                end
            end
        end

        -- Check if stock has changed for any selected seed OR if we haven't checked before
        for _, seedName in pairs(seedsToCheck) do
            local previousStock = lastStockCheck[seedName] or -1 -- Use -1 if never checked before

            if previousStock ~= currentStock[seedName] then
                lastStockCheck[seedName] = currentStock[seedName]
                print("Stock changed for " .. seedName .. ": " .. (currentStock[seedName] or 0))

                -- If any selected seed is available, trigger purchase
                if currentStock[seedName] and currentStock[seedName] > 0 then
                    shouldBuy = true
                    print("Will buy " .. seedName .. " - Stock: " .. currentStock[seedName])
                end
            end
        end

        -- Buy all available selected seeds if any stock changed
        if shouldBuy then
            print("Triggering purchase for available seeds")
            BuyAllSelectedSeeds()
        end
    end)
end

-- Function to stop stock checking
local function stopStockChecking()
    if stockCheckConnection then
        stockCheckConnection:Disconnect()
        stockCheckConnection = nil
    end
end

local Window = Fluent:CreateWindow({
    Title = "Fluent " .. Fluent.Version,
    SubTitle = "by XZery",
    TabWidth = 160,
    Size = UDim2.fromOffset(580, 460),
    Acrylic = true, -- The blur may be detectable, setting this to false disables blur entirely
    Theme = "Dark",
    MinimizeKey = Enum.KeyCode.LeftControl -- Used when theres no MinimizeKeybind
})

--Fluent provides Lucide Icons https://lucide.dev/icons/ for the tabs, icons are optional
local Tabs = {
    Main = Window:AddTab({ Title = "Main", Icon = "" }),
    Settings = Window:AddTab({ Title = "Settings", Icon = "settings" })
}

local Options = Fluent.Options
    
    local speed = Tabs.Main:AddSlider("Slider", {
        Title = "Speed",
        Description = "Set Your Speed",
        Default = 16,
        Min = 16,
        Max = 200,
        Rounding = 1,
    })

    speed:OnChanged(function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        char.Humanoid.WalkSpeed = Value
    end)

    speed:SetValue(16)

    local jump = Tabs.Main:AddSlider("Slider", {
        Title = "jump",
        Description = "Set Your JumpPower",
        Default = 50,
        Min = 50,
        Max = 200,
        Rounding = 1,
    })

    jump:OnChanged(function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        char.Humanoid.JumpPower = Value
    end)

    jump:SetValue(50)

    -- Fly toggle functionality
    local flyEnabled = false
    local bodyVelocity = nil
    local bodyAngularVelocity = nil
    local connection = nil

    local function enableFly()
        local player = game.Players.LocalPlayer
        local char = player.Character
        if not char or not char:FindFirstChild("HumanoidRootPart") then return end

        local humanoidRootPart = char.HumanoidRootPart

        -- Create BodyVelocity for movement
        bodyVelocity = Instance.new("BodyVelocity")
        bodyVelocity.MaxForce = Vector3.new(9e9, 9e9, 9e9)
        bodyVelocity.Velocity = Vector3.new(0, 0, 0)
        bodyVelocity.Parent = humanoidRootPart

        -- Create BodyAngularVelocity for rotation control
        bodyAngularVelocity = Instance.new("BodyAngularVelocity")
        bodyAngularVelocity.MaxTorque = Vector3.new(0, 9e9, 0)
        bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
        bodyAngularVelocity.Parent = humanoidRootPart

        -- Movement control
        connection = game:GetService("RunService").Heartbeat:Connect(function()
            local camera = workspace.CurrentCamera
            local userInputService = game:GetService("UserInputService")

            local velocity = Vector3.new(0, 0, 0)
            local flySpeed = 50

            -- Get camera directions
            local lookDirection = camera.CFrame.LookVector
            local rightDirection = camera.CFrame.RightVector
            local upDirection = Vector3.new(0, 1, 0)

            -- Simple directional movement based on camera
            if userInputService:IsKeyDown(Enum.KeyCode.W) then
                velocity = velocity + lookDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.S) then
                velocity = velocity - lookDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.A) then
                velocity = velocity - rightDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.D) then
                velocity = velocity + rightDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.Space) then
                velocity = velocity + upDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.LeftShift) then
                velocity = velocity - upDirection * flySpeed
            end

            bodyVelocity.Velocity = velocity
        end)
    end

    local function disableFly()
        if bodyVelocity then
            bodyVelocity:Destroy()
            bodyVelocity = nil
        end
        if bodyAngularVelocity then
            bodyAngularVelocity:Destroy()
            bodyAngularVelocity = nil
        end
        if connection then
            connection:Disconnect()
            connection = nil
        end
    end

    local flyToggle = Tabs.Main:AddToggle("FlyToggle", {
        Title = "Fly",
        Description = "Toggle fly mode",
        Default = false
    })

    flyToggle:OnChanged(function(Value)
        flyEnabled = Value
        if flyEnabled then
            enableFly()
        else
            disableFly()
        end
    end)

    -- Reset Character Button
    local resetButton = Tabs.Main:AddButton({
        Title = "Reset Character",
        Description = "Reset your character",
        Callback = function()
            local player = game.Players.LocalPlayer
            if player.Character then
                player.Character:BreakJoints()
            end
        end
    })

    -- Anti-AFK functionality
    local antiAfkEnabled = false
    local antiAfkConnection = nil
    local lastMoveTime = 0
    local isMoving = false
    local moveStartTime = 0

    local function startAntiAfk()
        antiAfkConnection = game:GetService("RunService").Heartbeat:Connect(function()
            if not antiAfkEnabled then return end

            local currentTime = tick()
            local player = game.Players.LocalPlayer
            local char = player.Character

            if char and char:FindFirstChild("Humanoid") and char:FindFirstChild("HumanoidRootPart") then
                local humanoid = char.Humanoid
                local rootPart = char.HumanoidRootPart

                -- Check if it's time to move (every 30 seconds)
                if not isMoving and (currentTime - lastMoveTime) >= 30 then
                    -- Start moving
                    isMoving = true
                    moveStartTime = currentTime
                    lastMoveTime = currentTime

                    -- Generate random direction
                    local randomX = math.random(-10, 10)
                    local randomZ = math.random(-10, 10)
                    local randomDirection = Vector3.new(randomX, 0, randomZ).Unit

                    -- Move in random direction
                    local targetPosition = rootPart.Position + (randomDirection * 5)
                    humanoid:MoveTo(targetPosition)
                end

                -- Stop moving after 2 seconds
                if isMoving and (currentTime - moveStartTime) >= 2 then
                    isMoving = false
                    humanoid:MoveTo(rootPart.Position) -- Stop at current position
                end
            end
        end)
    end

    local function stopAntiAfk()
        if antiAfkConnection then
            antiAfkConnection:Disconnect()
            antiAfkConnection = nil
        end
    end

    local antiAfkToggle = Tabs.Main:AddToggle("AntiAfkToggle", {
        Title = "Anti-AFK",
        Description = "Auto walk randomly every 30 seconds",
        Default = false
    })

    antiAfkToggle:OnChanged(function(Value)
        antiAfkEnabled = Value
        if antiAfkEnabled then
            startAntiAfk()
        else
            stopAntiAfk()
        end
    end)

    -- Seed stock dropdown (multi-select)
    local seedDropdown = Tabs.Main:AddDropdown("SeedDropdown", {
        Title = "Select Seeds",
        Description = "Choose which seeds to auto-buy (multi-select)",
        Values = {},
        Multi = true,
        Default = {},
    })

    -- Function to update seed dropdown
    local function updateSeedDropdown()
        local allSeeds = GetSeedStock(false) -- Get all seeds regardless of stock
        local seedList = {}
        
        for seedName, _ in pairs(allSeeds) do
            table.insert(seedList, seedName)
        end
        
        seedDropdown:SetValues(seedList)
    end

    -- Auto-buy toggle
    local autoBuyToggle = Tabs.Main:AddToggle("AutoBuyToggle", {
        Title = "Auto-Buy Seeds",
        Description = "Automatically buy selected seed when available",
        Default = false
    })

    autoBuyToggle:OnChanged(function(Value)
        autoBuyEnabled = Value
        if autoBuyEnabled then
            startStockChecking()
        else
            stopStockChecking()
        end
    end)

    -- Update seed dropdown when selection changes
    seedDropdown:OnChanged(function(Value)
        print("Dropdown OnChanged called")
        print("Value type:", type(Value))

        if type(Value) == "table" then
            print("Value contents:")
            for k, v in pairs(Value) do
                print("  ", k, "=", v, "(", type(k), "->", type(v), ")")
            end
        else
            print("Value:", Value)
        end

        SelectedSeedStock.Selected = Value -- Value is now a table of selected seeds
        -- Update stock data when selection changes
        GetSeedStock()
    end)

    -- Add the dropdown to Options for SaveManager
    Options.SeedDropdown = seedDropdown

    -- Initial seed dropdown update
    updateSeedDropdown()

    -- Load saved seed selections (will be loaded after SaveManager is properly initialized)

-- Addons:
-- SaveManager (Allows you to have a configuration system)
-- InterfaceManager (Allows you to have a interface managment system)

-- Hand the library over to our managers
SaveManager:SetLibrary(Fluent)
InterfaceManager:SetLibrary(Fluent)

-- Ignore keys that are used by ThemeManager.
-- (we dont want configs to save themes, do we?)
SaveManager:IgnoreThemeSettings()

-- You can add indexes of elements the save manager should ignore
SaveManager:SetIgnoreIndexes({})

-- use case for doing it this way:
-- a script hub could have themes in a global folder
-- and game configs in a separate folder per game
InterfaceManager:SetFolder("FluentScriptHub")
SaveManager:SetFolder("FluentScriptHub/specific-game")

InterfaceManager:BuildInterfaceSection(Tabs.Settings)
SaveManager:BuildConfigSection(Tabs.Settings)


Window:SelectTab(1)

Fluent:Notify({
    Title = "Fluent",
    Content = "The script has been loaded.",
    Duration = 8
})

-- You can use the SaveManager:LoadAutoloadConfig() to load a config
-- which has been marked to be one that auto loads!
SaveManager:LoadAutoloadConfig()